
    /**  
    * @Title: PuStkRecordBillDataSyncPlugin.java
    * @Package com.aisino.a6.business.plugin
    * @Description: TODO(用一句话描述该文件做什么)
    * <AUTHOR>
    * @date 2025年8月6日
    * @version V1.0  
    */
    
package com.aisino.a6.business.plugin;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aisino.a6.business.sa.util.plugin.SaBusinessUtil;
import com.aisino.a6.business.util.CommonUtil;
import com.aisino.aos.bill.common.BillUtils;
import com.aisino.aos.bill.vo.BillSetting;
import com.aisino.aos.preference.common.Prefer;
import com.aisino.aos.system.SessionHelper;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.CsUtil;
import com.aisino.platform.util.MapUtil;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;

/**
    * @ClassName: PuStkRecordBillDataSyncPlugin
    * @Description: TODO(入库数据同步)
    * <AUTHOR>
    * @date 2025年8月6日
    *
    */

public class PuStkRecordBillDataSync {
	public String billDataSync(AbstractForm form, DataMsgBus bus) {
		String alldata = bus.getString("alldata");
		DbSvr db = DbSvr.getDbService(null);
		BillSetting billSetting = BillUtils.getBillSettingVoByFormId("business_PU_stin_bill");
		Map data = new HashMap();
		String errmsg="";
		try{
			String jsonStr = URLDecoder.decode(alldata, "UTF-8");
			data = CsUtil.unserializeJson(jsonStr);
		}catch(Exception e){
			throw new BusinessException("明细行数太多，数据被截断，请分单填写。");
		}
		CommonUtil commonUtil = new CommonUtil();
		Map<String,Object> head = (Map) data.get("main");//主表
		String csupcode = MapUtil.getString(head, "csupcode");
		if(StringUtil.isBlank(csupcode)) {
			throw new BusinessException("供应商编码不能为空!");
		}
		String csupguid = commonUtil.checkCode("CM_Supplier", db, csupcode,"ccode");
		if(StringUtil.isBlank(csupguid)) {
			throw new BusinessException("供应商编码在A6中不存在!");
		}
		List<Map> detail = (List) data.get("detail");
		for (Map map : detail) {
			String czcpudetailid = CollectionUtil.getStringFromMap(map, "czcpudetailid");
			if (StringUtil.isBlank(czcpudetailid)) {
				throw new BusinessException("智采采购订单明细id不能为空!");
			}
		}
		Map map = detail.get(0);
		Map returnmap = new HashMap();
		String cpuordercode = MapUtil.getString(map, "cpuordercode");
		if (StringUtil.isBlank(cpuordercode)) {
			throw new BusinessException("采购订单号不能为空");
		}
		if (StringUtil.isNotBlank(cpuordercode)) {
			Map m=db.getOneRecorder("select * from PU_Order po where po.cCode =?",cpuordercode);
			if (CollectionUtil.isBlankMap(m)) {
				throw new BusinessException("单据不存在，可能已被并发删除");
			}
			
			Map cond=new HashMap();
			cond.put("cPushGUID", MapUtil.getString(m, "cguid"));
			List<String> czcsadetailids = new ArrayList<String>();
			for (Map item : detail) {
				String czcsadetailid = CollectionUtil.getStringFromMap(item, "czcpudetailid");
				if (czcsadetailid != null && !"".equalsIgnoreCase(czcsadetailid)) {
					czcsadetailids.add(czcsadetailid);
				}else{
					throw new BusinessException("智采明细id不能为空!");
				}
			}
			cond.put("czcsadetailids", czcsadetailids);
			List<Map> olist = db.queryIdForList("business_sa_saorderutil_sql.from_PuOrder", cond);
			if (CollectionUtil.isEmpty(olist)) {
				throw new BusinessException("采购订单号:"+cpuordercode+"已生成采购入库单。");
			}
			int iPricePrecision = Integer.parseInt(Prefer.get("U3_ST_PricePrecision"));
			for (Map ol : olist) {
				for (Map detailmap : detail) {
					String cstorecode = CollectionUtil.getStringFromMap(detailmap, "cstorecode");
					if(StringUtil.isBlank(cstorecode)) {
						throw new BusinessException("仓库编码不能为空!");
					}
					String cStoreGUID = commonUtil.checkCode("CM_Storehouse", db, cstorecode,"ccode");
					if(StringUtil.isBlank(cStoreGUID)) {
						throw new BusinessException("仓库编码在A6中不存在!");
					}
					String czcdetailid = MapUtil.getString(ol, "czcdetailid");
					String czcsadetailid = MapUtil.getString(detailmap, "czcpudetailid");
					if(StringUtil.isBlank(czcsadetailid)) {
						throw new BusinessException("智采明细id不能为空!");
					}
					if (czcsadetailid.equals(czcdetailid)) {
						BigDecimal iqty = CollectionUtil.getBigDecimal(ol, "iqty"); //数量
						BigDecimal itotal_f = CollectionUtil.getBigDecimal(detailmap, "itotal_f"); //含税金额
						BigDecimal itaxprice_f = CollectionUtil.getBigDecimal(detailmap, "itaxprice_f");//含税单价
						BigDecimal ipertaxrate = CollectionUtil.getBigDecimal(detailmap, "ipertaxrate"); //税率
						BigDecimal iamt_f = (itotal_f).divide(new BigDecimal("1").add(ipertaxrate.divide(new BigDecimal("100"))),iPricePrecision, RoundingMode.HALF_UP);//原币无税单价
						BigDecimal iunitprice_f=iamt_f.divide(iqty,iPricePrecision, RoundingMode.HALF_UP);//无税金额
						BigDecimal itax_f=itotal_f.subtract(iamt_f);//税额
						ol.put("iunitprice_f",iunitprice_f);
						ol.put("iunitprice",iunitprice_f);
						ol.put("itaxprice_f",itaxprice_f);
						ol.put("itaxprice",itaxprice_f);
						ol.put("iamt_f", iamt_f);
						ol.put("iamt", iamt_f);
						ol.put("itotal_f", itotal_f);
						ol.put("itotal", itotal_f);
						ol.put("ipertaxrate", ipertaxrate);
						ol.put("itax_f", itax_f);
						ol.put("itax", itax_f);
						ol.put("cStoreGUID", cStoreGUID);
						ol.put("czcdetailid", CollectionUtil.getStringFromMap(detailmap, "czcdetailid"));
					}
				}
				ol.put("csupguid", csupguid);
			}
			if(olist==null) return null;
			DataMsgBus genbus = new DataMsgBus();
			genbus.put("ruleId", "265177087948676103");
			genbus.put("bills", olist);
			genbus.put("batch", true);
			genbus.put("csupguid", csupguid);
			genbus.put("cCurGUID", "25050993204164451");
			genbus.put("iCurRate", 1.*********);
			genbus.setBusFormState("editnew");
			genbus.put("ifMergeGenBill", 0);
			genbus.put("cTaxType", "plus");
			genbus.put("cBusCode", "010");
			genbus.put("cBusType", "01001");
			genbus.put("cReObject", "17");
			genbus.put("cCreatorGUID", SessionHelper.getCurrentUserId());
			genbus.put("cBillType", "010");
			genbus.put("cSourceType", "076");
			genbus.put("cBusProcess", "260559476619242225");
			genbus.put("iInitFlag", "0");
			genbus.setNewAction("genFromBillGenList");
			db.update("update BILL_GEN_RULE set cSaveExcepType=? where ccode='puordertostkin'", new Object[]{"2"});
			List autoinv =  (List) AbstractForm.doFormSubmit("business_PU_stin_bill", genbus);
			form.sendMessage("审核后成功自动生成了采购入库单");
			if(SaBusinessUtil.isAutoCheck("business_PU_stin_bill")){//判断采购订单是否是自动审核
				for(int i=0;i<autoinv.size();i++){
					String cautodisguid = (String) autoinv.get(i);
					Map cInvMap = db.getOneRecorder("select * from ST_StkRecord where cguid=?", cautodisguid);
					String AutoChecker = SaBusinessUtil.getAutoChecker("business_PU_stin_bill");//取得自动审核人
					DataMsgBus nbus=new DataMsgBus();
					nbus.send("cGUID", cautodisguid);//自动生成订单的
					nbus.send("cTimeStamp", cInvMap.get("cTimeStamp"));//时间戳
					returnmap.put("billcode",cInvMap.get("cbillcode"));
					nbus.setNewAction("docheck");//调用单据审核事件
		            AbstractForm.callFormSubmitService("business_PU_stin_bill",nbus,false);
		            db.update("update PU_Order set cAuditorGUID = ? where cguid = ?", new Object[] {AutoChecker,cautodisguid});
				}
	        }
			db.update("update BILL_GEN_RULE set cSaveExcepType=? where ccode='puordertostkin'", new Object[]{"1"});
		}
		returnmap.put("status", "1");
		returnmap.put("errmsg", "");
		form.setReturn(returnmap);
		
		return null;
	}
}
