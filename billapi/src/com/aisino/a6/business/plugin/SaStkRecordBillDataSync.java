
    /**  
    * @Title: PuStkRecordBillDataSyncPlugin.java
    * @Package com.aisino.a6.business.plugin
    * @Description: TODO(用一句话描述该文件做什么)
    * <AUTHOR>
    * @date 2025年8月6日
    * @version V1.0  
    */
    
package com.aisino.a6.business.plugin;

import com.aisino.a6.business.sa.util.plugin.SaBusinessUtil;
import com.aisino.a6.business.util.CommonUtil;
import com.aisino.aos.bill.common.BillUtils;
import com.aisino.aos.bill.vo.BillSetting;
import com.aisino.aos.preference.common.Prefer;
import com.aisino.aos.system.SessionHelper;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.exception.BusinessException;
import com.aisino.platform.util.CollectionUtil;
import com.aisino.platform.util.CsUtil;
import com.aisino.platform.util.MapUtil;
import com.aisino.platform.util.StringUtil;
import com.aisino.platform.view.AbstractForm;
import com.aisino.platform.view.DataMsgBus;

import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

	/**
    * @ClassName: PuStkRecordBillDataSyncPlugin
    * @Description: TODO(出库数据同步)
    * <AUTHOR>
    * @date 2025年8月6日
    *
    */

public class SaStkRecordBillDataSync {
	public String billDataSync(AbstractForm form, DataMsgBus bus) {
		String alldata = bus.getString("alldata");
		BillSetting billSetting = BillUtils.getBillSettingVoByFormId("business_sa_sastkout");
		DbSvr db = DbSvr.getDbService(null);
		Map data = new HashMap();
		try{
			String jsonStr = URLDecoder.decode(alldata, "UTF-8");
			data = CsUtil.unserializeJson(jsonStr);
		}catch(Exception e){
			throw new BusinessException("明细行数太多，数据被截断，请分单填写。");
		}
		CommonUtil commonUtil = new CommonUtil();
		Map<String,Object> head = (Map) data.get("main");//主表
		String ccustcode = CollectionUtil.getStringFromMap(head, "ccustcode");
		if(StringUtil.isBlank(ccustcode)) {
			throw new BusinessException("客户编码不能为空!");
		}
		String cCustGUID = commonUtil.checkCode("CM_Customer", db, ccustcode,"ccode");
		if(StringUtil.isBlank(cCustGUID)) {
			throw new BusinessException("客户编码在A6中不存在!");
		}
		List<Map> detail = (List) data.get("detail");
		Map map = detail.get(0);
		Map returnmap = new HashMap();
		String csaordercode = MapUtil.getString(map, "csaordercode");
		if (StringUtil.isBlank(csaordercode)) {
			throw new BusinessException("销售订单号不能为空");
		}
		if (StringUtil.isNotBlank(csaordercode)) {
			Map m = db.getOneRecorder("select * from SA_Order so where so.cBillCode =?", csaordercode);
			if (CollectionUtil.isBlankMap(m)) {
				throw new BusinessException("单据不存在，可能已被并发删除");
			}
			Map cond=new HashMap();
			cond.put("cPushGUID", MapUtil.getString(m, "cguid"));

			List<String> czcsadetailids = new ArrayList<>();
			for (Map item : detail) {
				String czcsadetailid = CollectionUtil.getStringFromMap(item, "czcsadetailid");
				if (czcsadetailid != null && !"".equalsIgnoreCase(czcsadetailid)) {
					czcsadetailids.add(czcsadetailid);
				}else{
					throw new BusinessException("智采明细id不能为空!");
				}
			}
			cond.put("czcsadetailids", czcsadetailids);
			List<Map> olist = db.queryIdForList("business_sa_saorderutil_sql.from_SaOrder", cond);
			if (CollectionUtil.isEmpty(olist)) {
				throw new BusinessException("销售订单号:"+csaordercode+"已生成全部生成销售出库单");
			}
			int iPricePrecision = Integer.parseInt(Prefer.get("U3_ST_PricePrecision"));
			for (Map ol : olist) {
				for (Map detailmap : detail) {
					String cstorecode = CollectionUtil.getStringFromMap(detailmap, "cstorecode");
					if(StringUtil.isBlank(cstorecode)) {
						throw new BusinessException("仓库编码不能为空!");
					}
					String cStoreGUID = commonUtil.checkCode("CM_Storehouse", db, cstorecode,"ccode");
					if(StringUtil.isBlank(cStoreGUID)) {
						throw new BusinessException("仓库编码在A6中不存在!");
					}
					String czcdetailid = MapUtil.getString(ol, "czcdetailid");
					String czcsadetailid = MapUtil.getString(detailmap, "czcsadetailid");
					if(StringUtil.isBlank(czcsadetailid)) {
						throw new BusinessException("智采明细id不能为空!");
					}
					if (czcsadetailid.equals(czcdetailid)) {
						BigDecimal iqty = CollectionUtil.getBigDecimal(detailmap, "iqty"); //数量
						BigDecimal itaxprice_f = CollectionUtil.getBigDecimal(ol, "itaxprice_f");//含税单价
						BigDecimal iunitprice_f = CollectionUtil.getBigDecimal(ol, "iunitprice_f");//含税单价

						BigDecimal itotal_f = iqty.multiply(itaxprice_f); //含税金额
						BigDecimal iamt_f = iqty.multiply(iunitprice_f);
						BigDecimal itax_f = itotal_f.subtract(iamt_f);//税额

						ol.put("iqty", iqty);
						ol.put("iunitqty", iqty);
						ol.put("irefepriceqty", iqty);
						ol.put("iamt_f", iamt_f);
						ol.put("iamt", iamt_f);
						ol.put("itotal_f", itotal_f);
						ol.put("itotal", itotal_f);
						ol.put("itax_f", itax_f);
						ol.put("itax", itax_f);
						ol.put("cStoreGUID", cStoreGUID);
					}
				}
				ol.put("ccustguid", cCustGUID);
			}
			DataMsgBus genbus = new DataMsgBus();
			genbus.put("ruleId", "256523677040854357");
			genbus.put("bills", olist);
			genbus.put("batch", true);
			genbus.put("ccustguid", cCustGUID);
			genbus.put("cCurGUID", "25050993204164451");
			genbus.put("iCurRate", 1.*********);
			genbus.setBusFormState("editnew");
			genbus.put("ifMergeGenBill", 0);
			genbus.put("cBusCode", "020");
			genbus.put("cBusType", "02001");
			genbus.put("cCreatorGUID", SessionHelper.getCurrentUserId());
			genbus.put("cBillType", "020");
			genbus.put("cSourceType", "066");
			genbus.put("cBusProcess", "02001");
			genbus.put("iInitFlag", "0");
			genbus.put("iRedFlag", "0");
			genbus.setNewAction("genFromBillGenList");
			db.update("update BILL_GEN_RULE set cSaveExcepType=? where ccode='saorder2sastkout'", new Object[]{"2"});
			List autoinv =  (List) AbstractForm.doFormSubmit("business_sa_sastkout", genbus);
			if(SaBusinessUtil.isAutoCheck("business_sa_sastkout")){
				for(int i=0;i<autoinv.size();i++){
					String cautodisguid = (String) autoinv.get(i);
					Map cInvMap = db.getOneRecorder("select * from ST_StkRecord where cguid=?", cautodisguid);
					String AutoChecker = SaBusinessUtil.getAutoChecker("business_sa_sastkout");//取得自动审核人
					DataMsgBus nbus=new DataMsgBus();
					nbus.send("cGUID", cautodisguid);//自动生成订单的
					nbus.send("cTimeStamp", cInvMap.get("cTimeStamp"));//时间戳
					returnmap.put("billcode",cInvMap.get("cBillCode"));
					nbus.setNewAction("docheck");//调用单据审核事件
					AbstractForm.callFormSubmitService("business_sa_sastkout",nbus,false);
					db.update("update ST_StkRecord set cAuditorGUID = ? where cguid = ?", new Object[] {AutoChecker,cautodisguid});
				}
			}
			db.update("update BILL_GEN_RULE set cSaveExcepType=? where ccode='saorder2sastkout'", new Object[]{"1"});
			returnmap.put("status", "1");
			returnmap.put("errmsg", "");
			form.setReturn(returnmap);
		}
		return null;
	}
}
