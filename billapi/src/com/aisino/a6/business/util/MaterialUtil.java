package com.aisino.a6.business.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.cxf.binding.corba.wsdl.Object;

import com.aisino.platform.core.Guid;
import com.aisino.platform.db.DbSvr;
import com.aisino.platform.util.CollectionUtil;

public class MaterialUtil {
	
	public Map addMateril(String cmatcode, String cmatname, String cunitname){
		DbSvr db = DbSvr.getDbService(null);
		String matclasscode = "000";
		Map matcmap = db.getOneRecorder("select cguid , cFullCode from CM_MatClass where ccode=?", matclasscode);
		String matclassguid = CollectionUtil.getStringFromMap(matcmap, "cguid");
		String cfullcode = CollectionUtil.getStringFromMap(matcmap, "cfullcode")+"!"+cmatcode;
		//获取物品单位guid
		String unitguid = checkUnit(cunitname,db);
		
		String sql = "insert into CM_Material (iPurchase,iCkNo,iSelf,iChangeRateWay,cInvName,iCtrlStoreQTY,cHotCode,iXSSSYHZC,"
				+ "iGuaranteeFlag,cName,cParentID,iValid,dCreateDate,iRopMark,iPrecision,cPURefePriceMethod,cGUID,cCode,iBatchFlag,"
				+ "iUniqueNo,iRkNo,iFetchWay,idigno,iOutsource,iAssembly,cMatCode,cMatCGUID,cPuUGUID,iStatus,cMatName,csarefepricemethod,"
				+ "sRoutingmark,iMACheck,iPUCheck,cSaUGUID,iProductWay,cEmployeeGUID,iSubUnit,IENDBOM,iSNStart,iCalWay,iSendToDevice,iLeaf,"
				+ "iServisFlag,cUnitGUID,iTaxRate,idigNoCtrl,iCreatBom,iQuntEqu,cStkUnitID,cTimeStamp) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,"
				+ "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
		Map matmap = new HashMap();
		String matguid = Guid.g();
		matmap.put("iPurchase", 0);
		matmap.put("iCkNo", 0);
		matmap.put("iSelf", 0);
		matmap.put("iChangeRateWay", 1);
		matmap.put("cInvName", cmatname);
		matmap.put("iCtrlStoreQTY", 1);
		matmap.put("cHotCode", "");
		matmap.put("iXSSSYHZC", 0);
		matmap.put("iGuaranteeFlag", 0);
		matmap.put("cName", cmatname);
		matmap.put("cParentID", 000000);
		matmap.put("iValid", 1);
		matmap.put("dCreateDate", "2025-08-14 00:00:00");
		matmap.put("iRopMark", 0);
		matmap.put("iPrecision", 2);
		matmap.put("cPURefePriceMethod", "z");
		matmap.put("cGUID", matguid);
		matmap.put("cCode", cmatcode);
		matmap.put("iBatchFlag", 0);
		matmap.put("iUniqueNo", 1);
		matmap.put("iRkNo", 0);
		matmap.put("iFetchWay", 0);
		matmap.put("idigno", 8);
		matmap.put("iOutsource", 0);
		matmap.put("iAssembly", 0);
		matmap.put("cMatCode", cmatcode);
		matmap.put("cMatCGUID", matclassguid);
		matmap.put("cPuUGUID", unitguid);
		matmap.put("iStatus", 1);
		matmap.put("cMatName", cmatname);
		matmap.put("csarefepricemethod", "z");
		matmap.put("sRoutingmark", 0);
		matmap.put("iMACheck", 0);
		matmap.put("iPUCheck", 0);
		matmap.put("cSaUGUID", unitguid);
		matmap.put("iProductWay", 0);
		matmap.put("cEmployeeGUID", 1);
		matmap.put("iSubUnit", 0);
		matmap.put("IENDBOM", 0);
		matmap.put("iSNStart", 0);
		matmap.put("iCalWay", 2);
		matmap.put("iSendToDevice", 0);
		matmap.put("iLeaf", 1);
		matmap.put("iServisFlag", 0);
		matmap.put("cUnitGUID", unitguid);
		matmap.put("iTaxRate", 13.0);
		matmap.put("idigNoCtrl", 0);
		matmap.put("iCreatBom", 0);
		matmap.put("iQuntEqu", 0);
		matmap.put("cStkUnitID", unitguid);
		matmap.put("cFullCode", cfullcode);
		matmap.put("cTimeStamp", System.currentTimeMillis());
		try{
			db.insertRow("CM_Material", matmap);
			//db.update(sql, matmap);
			db.update("delete from CM_MatUnitRate where cMatGUID=?", matguid);
			String ratesql = "insert into CM_MatUnitRate (cGUID,cMatGUID,cUnitGUID,cUnitName,iChangeRate) values(?,?,?,?,?)";
			Map ratemap = new HashMap();
			ratemap.put("cGUID", Guid.g());
			ratemap.put("cMatGUID", matguid);
			ratemap.put("cUnitGUID", unitguid);
			ratemap.put("cUnitName", cunitname);
			ratemap.put("iChangeRate", 1.00);
			db.insertRow("CM_MatUnitRate", ratemap);
			db.update("delete from ST_SerialNumber where cMatGUID = ?", matguid);
			db.update("delete from meta_keyattr where cMatGuid = ?", matguid);
			db.update("delete from CM_MatStkQTY where cMatGUID = ?", matguid);
			
		}catch(Exception e){
			System.out.println("新增物品["+cmatname+"]报错...");
			e.printStackTrace();
		}
		
		Map rtn = new HashMap();
		rtn.put("matguid", matguid);
		return rtn;
	}
	
	/***
	 * 获取计量单位cguid
	 * @param cunitname
	 * @param db
	 * @return cguid
	 */
	public String checkUnit(String cunitname, DbSvr db){
		String cguid = db.getStringResult("SELECT cguid FROM CM_Unit WHERE cName=?", cunitname);
		if(cguid==null || "".equals(cguid)){
			String newguid = Guid.g();
			String ccode = unitcode(db);
			Map unitmap = new HashMap();
			unitmap.put("cCode", ccode);
			unitmap.put("cGUID", newguid);
			unitmap.put("iStatus", 1);
			unitmap.put("iChangeRate", 1.0000);
			unitmap.put("cClassGUID", "152033862454026320");
			unitmap.put("cName", cunitname);
			unitmap.put("iBasicFlag", 0);
			unitmap.put("cTimeStamp", System.currentTimeMillis());
			try{
				db.insertRow("CM_Unit", unitmap);
				cguid = newguid;
			}catch(Exception e){
				System.out.println("新增计量单位["+cunitname+"]报错...");
				e.printStackTrace();
			}
		}
		return cguid;
	}
	
	/**
	 * 获取最大的编码号
	 * @param db
	 * @return maxcode
	 */
	public String unitcode(DbSvr db){
		// 编码前缀
	    final String CODE_PREFIX = "hw_";
	    // 数字部分的固定长度
	    final int NUMBER_LENGTH = 4;

	    String  codemax = db.getStringResult("SELECT max(cCode) codemax from CM_Unit WHERE cCode LIKE 'hw_%'");
		if(codemax==null || codemax.isEmpty()){
			return "hw_0001";
		}else{
			int currentNumber = 0;
			String newcode = codemax;
			String numberStr = codemax.substring(CODE_PREFIX.length());
            currentNumber = Integer.parseInt(numberStr);
            int nextNumber = currentNumber + 1;
            newcode = CODE_PREFIX + String.format("%0" + NUMBER_LENGTH + "d", nextNumber);
			return newcode;
		}
	}


}
