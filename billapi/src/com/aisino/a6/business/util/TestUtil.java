
    /**  
    * @Title: qq.java
    * @Package com.aisino.a6.finance.data.util
    * @Description: TODO(用一句话描述该文件做什么)
    * <AUTHOR>
    * @date 2025年8月7日
    * @version V1.0  
    */
    
package com.aisino.a6.business.util;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.druid.util.Base64;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;


    /**
    * @ClassName: qq
    * @Description: TODO(这里用一句话描述这个类的作用)
    * <AUTHOR>
    * @date 2025年8月7日
    *
    */

public class TestUtil {
	
	    public static void main(String[] args) {
	        try {
	            // 1. 准备JSON字符串
	            String jsonStr = "{\"billtype\":\"PU_Order\",\"DBName\":\"0310\",\"main\":{\"cBillCode\":\"XD202508000004\",\"csupcode\":\"000001\",\"ddate\":\"2022-09-13\",\"ccustcode\":\"000001\",\"cinvcust\":\"未来科技有限公司\",\"ccreatorcode\":\"admin\",\"cregcode\":\"*********\",\"cremark\":\"小申测试\"},\"detail\":[{\"cmatcode\":\"000001\",\"cmatname\":\"笔记本电脑\",\"cunitcode\":\"pf\",\"cunitname\":\"平方\",\"iqty\":\"100.00\",\"iunitprice_f\":\"2.00\",\"itaxprice_f\":\"2.00\",\"ipertaxRate\":\"13\",\"iamt_f\":\"200.00\",\"itax_f\":\"200.00\",\"itotal_f\":\"200.00\",\"cstorecode\":\"001\",\"iunitprice\":\"2.00\",\"itaxprice\":\"2.00\",\"iamt\":\"2.00\",\"itax\":\"2.00\",\"itotal\":\"2.00\",\"cremark\":\"备注\",\"cdefine12\":\"\",\"cdefine13\":\"\",\"cdefine14\":\"\",\"cdefine15\":\"\",\"cdefine16\":\"\",\"csaordercode\":\"XD202508000007\"}]}";
	            ObjectMapper objectMapper = new ObjectMapper();

				Map<String, Object> bill = new HashMap<>();

				// Add top level properties
				bill.put("billtype", "SA_StkRecord");
				bill.put("DBName", "0310");

				// Create and populate main map
				Map main = new HashMap<>();
				main.put("cBillCode", "XD202508000004");
				main.put("csupcode", "000001");
				main.put("ddate", "2022-09-13");
				main.put("ccustcode", "000001");
				main.put("cinvcust", "未来科技有限公司");
				main.put("ccreatorcode", "admin");
				main.put("cregcode", "*********");
				main.put("cremark", "小申测试");

				bill.put("main", main);

				// Create and populate detail list
				List<Map> detailList = new ArrayList<>();
				Map detail = new HashMap<>();

				detail.put("cmatcode", "000001");
				detail.put("cmatname", "笔记本电脑");
				detail.put("cunitcode", "pf");
				detail.put("cunitname", "平方");
				detail.put("iqty", "20.00");
				detail.put("iunitprice_f", "2.00");
				detail.put("itaxprice_f", "2.00");
				detail.put("ipertaxRate", "13");
				detail.put("iamt_f", "200.00");
				detail.put("itax_f", "200.00");
				detail.put("itotal_f", "200.00");
				detail.put("cstorecode", "000001");
				detail.put("iunitprice", "2.00");
				detail.put("itaxprice", "2.00");
				detail.put("iamt", "2.00");
				detail.put("itax", "2.00");
				detail.put("itotal", "2.00");
				detail.put("cremark", "备注");
				detail.put("cdefine12", "");
				detail.put("cdefine13", "");
				detail.put("cdefine14", "");
				detail.put("cdefine15", "");
				detail.put("cdefine16", "");
				detail.put("csaordercode", "XD202508000001");
				detail.put("czcsadetailid", "12345");
				detailList.add(detail);
				bill.put("detail", detailList);


	            Gson gson = new Gson();
	            String json = gson.toJson(bill);
				String encodedBytes = Base64.byteArrayToBase64(json.getBytes());
	            System.out.println("json编码: " + json);
	            System.out.println("Base64编码: " + encodedBytes);
	            // 2. 对JSON进行URL编码
	            String encodedJson = URLEncoder.encode(encodedBytes, StandardCharsets.UTF_8.toString());
	            
	            // 3. 构建请求URL
	            String urlStr = "http://127.0.0.1:8889/A6/pt/service?formid=interface_datasync_form&alldata=" + encodedJson;
	            System.out.println("urlStr编码: " + urlStr);
	            // 4. 创建并配置HTTP连接
	            URL url = new URL(urlStr);
	            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
	            conn.setRequestMethod("GET");
	            conn.setRequestProperty("Accept-Charset", "UTF-8");
	            
	            // 5. 发送请求并获取响应
	            int responseCode = conn.getResponseCode();
	            System.out.println("响应码: " + responseCode);
	            
	            BufferedReader in = new BufferedReader(
	                new InputStreamReader(conn.getInputStream()));
	            String inputLine;
	            StringBuilder response = new StringBuilder();
	            
	            while ((inputLine = in.readLine()) != null) {
	                response.append(inputLine);
	            }
	            in.close();
	            
	            // 6. 输出响应结果
	            System.out.println("响应内容: " + response.toString());
	            
	        } catch (Exception e) {
	            e.printStackTrace();
	        }
	}
}
