<?xml version="1.0" encoding="UTF-8"?>
<sqls>
	<sql group="business_sa_saorderutil_sql" desp="杂项">

        <i id="canAutoInv" desp="销售订单是否可以自动生成采购订单">
            <![CDATA[
            select
            c.cSystemFlowGUID as cSystemFlowGUID
            from CO_BusinessProcess c
            inner join SA_Order i on i.cBusType = c.cBusType and i.cBusProcess = c.cGUID
            left join CO_Process a on a.cProcessID=c.cSystemFlowGUID
            where i.cguid = ?
            and (
                (a.cDBillType = '085' and a.cSBillType = '076')
              or c.cSystemFlowGUID='00001'
            )
        ]]>
        </i>
        <i id="from_SaOrder" desp="销售订单生成蓝字销售出库单">
            <![CDATA[
            select l.cguid,
                   l.cheadguid,
			       convert(char(10), o.dDate, 120) as RDate,/*日期*/
			       convert(char(10), l.dPlanDispatchDate, 120) as dPlanDispatchDate,/*预发货日期*/
			       o.cBillCode,/*订单号*/
			       cbt.cname as cbustype_show,/*业务类型*/
				   cbp.cname as cbusprocess_show,/*业务流程*/
			       o.cTimeStamp,
			       c.cName as cCustName,/*客户名称*/
			       c.cCode as cCustCode,/*客户编码*/
			       o.cConsigCompany as cConsigCompany,/*收货单位*/
			       l.iNumber as iNumber,/*行号*/
			       de.cName as cDeptName,/*部门*/
			       e.cName as cEmpName,/*职员*/
			       t.cName as cSalType,
			       m.cMatCode as cMatCode,/*物品编码*/
			       m.cMatName as cMatName,/*物品名称*/
			       m.cSpec as cSpec,/*规格型号*/
			       m.iBatchFlag,
			       m.iSubUnit,
			       m.iPrecision,
			       m.iGuaranteeFlag,
			       case when proinfo.iGuaranteeDays is not null then proinfo.iGuaranteeDays
			       else cast(m.iGuaranteePeriod as numeric(21,0)) end as iGuaranteeDays,
			       u.cName as cMUnitName,/*主单位*/
			       isnull(l.iUnitQTY,0) as iOrderQTY,/*数量*/
			       isnull(l.iOutQTY,0) as iOutQTY,/*累计出库数量*/
			       isnull(exdispatch.iExchangeQTY,0) as iReOutQTY,/*出库退补数量*/
			       /*(l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0))) as iUnitQTY_K,可出库数量*/

			       case when {isHaveOutOrder:'1'}=1 and (l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0)))=0 or (l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0)))<0 then '0'
			            else (l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0)))
			       end as iUnitQTY_K,

			       case when {isHaveOutOrder:'1'}=1 and (l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0)))=0 or (l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0)))<0 then '0'
			       		else (l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0)))
			       end as iUnitQTY,/*本次出库数量*/

			       gl.cName as cCurName,/*币种*/
			       case when isnull(l.iOutQTY, 0)=0 then l.iQTY
			            else ((l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0))) / l.iChangeRate)
			       end as PlanQTY,
			       case when isnull(l.iOutQTY, 0)=0 then l.iQTY
			       		when ((l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0))) / l.iChangeRate)<0 then '0'
			            else ((l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0))) / l.iChangeRate)
			       end as iQTY,
			       l.csarefepricemethod,
			       l.cSaRefePriceUnitGUID,
			       case when isnull(l.iOutQTY, 0)=0 then l.irefepriceqty
			            when l.csarefepricemethod='f' then ((l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0))) / l.iChangeRate)
			            when (l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0))) < 0 then '0'
			            else (l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0)))
			       end as irefepriceqty,
			       l.iQuotedPrice,
			       l.iTaxQuotedPrice,
			       l.iPerTaxRate as iTaxRate,
			       l.iTaxPrice_F,
			       case when isnull(l.iOutQTY, 0)=0 then l.iTotal_F
			            when l.csarefepricemethod='f' then ((l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0))) / l.iChangeRate)*l.iTaxPrice_F
			            when (l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0)))*l.iTaxPrice_F < 0 then '0'
			            else (l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0)))*l.iTaxPrice_F
			       end as  iTotal_F,
			       case when isnull(l.iOutQTY, 0)=0 then l.iAMT_F
			            when l.csarefepricemethod='f' then ((l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0))) / l.iChangeRate)*l.iTaxPrice_F/(l.iPerTaxRate / 100 + 1)
			            when (l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0)))*l.iTaxPrice_F/(l.iPerTaxRate / 100 + 1)<0 then '0'
			            else (l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0)))*l.iTaxPrice_F/(l.iPerTaxRate / 100 + 1)
			       end as  iAMT_F,
			       case when isnull(l.iOutQTY, 0)=0 then l.iUnitPrice_F
			            else l.iTaxPrice_F/(l.iPerTaxRate / 100 + 1)
			       end as iUnitPrice_F,
			       case when isnull(l.iOutQTY, 0)=0 then l.iTax_F
			            when l.csarefepricemethod='f' then ((l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0))) / l.iChangeRate)*l.iTaxPrice_F*(1-1/(l.iPerTaxRate / 100 + 1))
			            when (l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0)))*l.iTaxPrice_F*(1-1/(l.iPerTaxRate / 100 + 1))<0 then '0'
			            else (l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0)))*l.iTaxPrice_F*(1-1/(l.iPerTaxRate / 100 + 1))
			       end as  iTax_F,
			       case when isnull(l.iOutQTY, 0)=0 then l.irefepriceqty*isnull(l.iTaxQuotedPrice,0)
			            when l.csarefepricemethod='f' then ((l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0))) / l.iChangeRate)*isnull(l.iTaxQuotedPrice,0)
			            else (l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0)))*isnull(l.iTaxQuotedPrice,0)
			       end as iQuotedTotal_F,
			       case when isnull(l.iOutQTY, 0)=0 or isnull(l.iTaxQuotedPrice,0)=0 then l.iDisAMT_F
			            when l.csarefepricemethod='f'  then ((l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0))) / l.iChangeRate)*isnull(l.iTaxQuotedPrice,0)-((l.iUnitQTY - isnull(l.iOutQTY, 0)) / l.iChangeRate)*l.iTaxPrice_F
			            else  (l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0)))*isnull(l.iTaxQuotedPrice,0)-(l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0)))*l.iTaxPrice_F
			       end as iDisAMT_F,
			       l.iDisRate,
			       l.iDisRateCon,
			       case when isnull(l.iOutQTY, 0)=0 then l.iMUnitPrice_F
			            when l.csarefepricemethod='f' then l.iTaxPrice_F/l.iChangeRate/(l.iPerTaxRate / 100 + 1)
			            else l.iTaxPrice_F/(l.iPerTaxRate / 100 + 1)
			       end as iMUnitPrice_F,

			       l.iChangeRate,


			       l.iTaxPrice,
			       case when isnull(l.iOutQTY, 0)=0 then l.iTotal
			            when l.csarefepricemethod='f' then ((l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0))) / l.iChangeRate)*l.iTaxPrice
			            else (l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0)))*l.iTaxPrice
			       end as  iTotal,
			       case when isnull(l.iOutQTY, 0)=0 then l.iAMT
			            when l.csarefepricemethod='f' then ((l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0))) / l.iChangeRate)*l.iTaxPrice/(l.iPerTaxRate / 100 + 1)
			            else (l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0)))*l.iTaxPrice/(l.iPerTaxRate / 100 + 1)
			       end as  iAMT,
			       case when isnull(l.iOutQTY, 0)=0 then l.iUnitPrice
			            else l.iTaxPrice/(l.iPerTaxRate / 100 + 1)
			       end as iUnitPrice,
			       case when isnull(l.iOutQTY, 0)=0 then l.iTax
			            when l.csarefepricemethod='f' then ((l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0))) / l.iChangeRate)*l.iTaxPrice*(1-1/(l.iPerTaxRate / 100 + 1))
			            else (l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0)))*l.iTaxPrice*(1-1/(l.iPerTaxRate / 100 + 1))
			       end as  iTax,
			       case when isnull(l.iOutQTY, 0)=0 or isnull(l.iTaxQuotedPrice,0)=0 or l.iTotal=0 then l.iDisAMT
			            when l.csarefepricemethod='f'  then ((l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0))) / l.iChangeRate)*isnull(l.iTaxQuotedPrice,0)*l.iTotal/l.iTotal_F-((l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0))) / l.iChangeRate)*l.iTaxPrice
			            else  (l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0)))*isnull(l.iTaxQuotedPrice,0)*l.iTotal/l.iTotal_F-(l.iUnitQTY - isnull(l.iOutQTY, 0)+ abs(isnull(exdispatch.iExchangeQTY,0)))*l.iTaxPrice
			       end as iDisAMT,
			       case when isnull(l.iOutQTY, 0)=0 then l.iMUnitPrice
			            when l.csarefepricemethod='f' then l.iTaxPrice/l.iChangeRate/(l.iPerTaxRate / 100 + 1)
			            else l.iTaxPrice/(l.iPerTaxRate / 100 + 1)
			       end as iMUnitPrice,
   		           case when o.cPayTime is not null then o.cPayTime 		/*当来源单据付款时间有值，则直接带订单付款时间 A6.2 WJ 20151102*/
	       				when c.iCreDate is not null then DATEADD(day, c.iCreDate, CONVERT(varchar(100), o.ddate, 112))
       		    		else CONVERT(varchar(100), o.ddate, 112)
	       		   end as cpaytime_fj,	/*付款时间附加项*/
			       u.cClassGUID,
			       cu.iRateFlag,
			       l.cBatchGUID as cBatchGUID,
				   proinfo.dOverDate AS dOverDate,
				   proinfo.dProduction as dProduction,
			       o.cRemark cRemark_copyitem,
			       l.cRemark cLineRemark,
                   s.cCode as cStoreCode,/*仓库编码*/
			       s.cName as cStoreName,/*仓库名称*/
			       isnull(l.cStoreGUID,m.cStoreGUID) as cStoreGUID,
			       case when l.cStoreGUID is null then m.cPositionGUID
			            when m.cStoreGUID = l.cStoreGUID then m.cPositionGUID
			       		else null end as cPositionGUID,
			       case when l.cStoreGUID is null then p.cName
			            when m.cStoreGUID = l.cStoreGUID then p.cName
			       		else null end as crowposinfo,
			       l.czcdetailid
			       @field[o.*,l.*]
                   @flex[o]
                   @flex[l]
			  from SA_Order o
			 LEFT JOIN CM_Customer c ON o.cCustGUID = c.cGUID
			  LEFT JOIN CM_Department de ON o.cDeptGUID = de.cGUID
			  LEFT JOIN CM_Employee e ON o.cEmpGUID = e.cGUID
			  LEFT JOIN CO_SaleType t ON o.cSalTypeGUID = t.cGUID
			 LEFT JOIN AOS_RMS_USER p1 ON o.cCreatorGUID = p1.cGUID
			 LEFT JOIN AOS_RMS_USER p2 ON o.cAuditorGUID = p2.cGUID
			 INNER JOIN SA_orderLine l ON o.cGUID = l.cHeadGUID
			 LEFT JOIN CM_Material m ON l.cMatGUID = m.cGUID
			 left join CM_Position p on m.cPositionGUID = p.cGUID
			 LEFT JOIN CM_Storehouse s ON l.cStoreGUID = s.cGUID
			 LEFT JOIN (
			 	SELECT
			 	qtyinfo.cMatGUID,
			 	qtyinfo.cBatchGUID,
			 	CONVERT(char(10), MAX(qtyinfo.dProduction), 120) dProduction,
			 	CONVERT(char(10), MAX(qtyinfo.dOverDate), 120) dOverDate,
			 	MAX(qtyinfo.iGuaranteeDays) iGuaranteeDays
			 	FROM ST_CurrentStock qtyinfo
			 	WHERE qtyinfo.cBatchGUID IS NOT NULL AND qtyinfo.dProduction IS NOT NULL
			 	AND qtyinfo.dOverDate IS NOT NULL
			 	GROUP BY qtyinfo.cMatGUID,qtyinfo.cBatchGUID
			 ) AS proinfo on proinfo.cMatGUID=l.cMatGUID AND proinfo.cBatchGUID=l.cBatchGUID
			 INNER JOIN CM_Unit u  ON l.cMUnitGUID = u.cGUID
			  left JOIN CM_UnitClass cu on u.cClassGUID = cu.cGUID
			  LEFT JOIN CM_Unit u1 ON l.cUnitGUID = u1.cGUID
			  LEFT JOIN(
				/*出库单退补数量*/
		       	SELECT ord2stk.cSLineID                                                                                       AS cOrdGUID,
				       Sum(Cast(dbo.F_getrelationvalue(stk2stk.iExeValue, ord2stk.cExeFields, 'iunitqty') AS NUMERIC(21, 9))) iExchangeQTY
				FROM   BILL_GEN_RELATION_MAIN ord2stk
				       LEFT JOIN BILL_GEN_RELATION_MAIN stk2stk
				         ON stk2stk.cSLineID = ord2stk.cDLineID
				       LEFT JOIN ST_StkRecordLine stk
				         ON stk2stk.cDLineID = stk.cGUID
				WHERE  ord2stk.cSMainEntity = 'sa_order'
				       AND ord2stk.cDMainEntity = 'st_stkrecord'
				       AND stk2stk.cSMainEntity = 'st_stkrecord'
				       AND stk2stk.cDMainEntity = 'st_stkrecord'
				       AND stk.iReturnFlag = '1'
				       AND stk.cSAStkOutCode IS NOT NULL
				GROUP  BY ord2stk.cSLineID
				) AS exdispatch ON exdispatch.cOrdGUID = l.cguid
			  left join CO_BusinessProcess cbp on cbp.cBusType = o.cBusType and cbp.cGUID = o.cBusProcess
			  left join CO_BusinessType cbt on o.cBusType=cbt.cguid
  			  left join CO_Process cp on cp.cProcessID = cbp.cSystemFlowGUID and cp.cSBillType='066' and cp.cDBillType='020'
			  left join GL_Currency gl on gl.cguid=o.cCurGUID
			  @table[SA_Order o,SA_orderLine l,CM_Material m]
			 where  ('1' = {isHaveOutOrder:'2'} or (abs(l.iUnitQTY)+ abs(isnull(exdispatch.iExchangeQTY,0))) > abs(ISNULL(l.iOutQTY,0)))
			   and o.iAuditStatus = 'checked'
			   and isnull(l.iCloseFlag,0) = '0'
			   and $equal(o.cBusProcess,cBusProcess)
			   and $equal(o.cBusType,cBusType)
			   and (l.cCloserGUID is null or l.cCloserGUID='')
			   and m.iServisFlag = '0'
			   and $between(s.cCode,minStore,maxStore)
			   and $equal(o.cguid,cPushGUID)
			   and $in(l.czcdetailid,czcsadetailids)
			   and $in(l.cStoreGUID,right_cStorGUID)
   			   and $equal(p1.cGUID,cCreatorGUID)
   			   and $equal(p2.cGUID,cAuditorGUID)
   			   and $equal(o.cSalTypeGUID,cSalTypeGUID)
   			   and $equal(l.cguid,cguid)
   			   and $equal(o.cInvCust,cInvCust)
			   and $between(o.cBillCode,cBillCodeBegin,dBillCodeEnd)
			   and $between(o.dDate,startDate,endDate)
			   and $between(l.dPlanDispatchDate,dPlanDispatchStartDate,dPlanDispatchEndDate)
			   and $between(o.dCheckTime,startCheckDate,endCheckDate)
			   and $between(c.cCode,minCust,maxCust)
			   and $between(m.cMatCode,minMat,maxMat)
			   and $between(de.cCode,minDept,maxDept)
			   and $between(e.cCode,minEmp,maxEmp)
			   and $like(o.cRemark,cRemark_head)
			   and $like(l.cRemark,cRemark_line)
			   and $equal(l.cItemGUID,cItemGUID)
               and ('*' = {cMatClass:'*'} or  m.cFullCode like (SELECT cFullCode+'!%' FROM CM_MatClass cmc2 WHERE cCode = {cMatClass:''}))
               and (
               /*业务流程不为无，直接受业务流程控制*/
			   cbp.cSystemFlowGUID!='00001'
               or (/*没有生成过发货单*/
               	not exists (select * from BILL_GEN_RELATION_MAIN r0 where r0.cDMainEntity='SA_Dispatch' and r0.cSMainID=o.cguid)
                and
               	not exists (/*生成过发票*/
               		select * from BILL_GEN_RELATION_MAIN r1
               		where r1.cDMainEntity='SA_Invoice' and r1.cSMainID=o.cguid
               		and exists(/*发票生成过发货单或者出库单*/
	           			select * from BILL_GEN_RELATION_MAIN r2 where r2.cSMainID=r1.cDMainID and r2.cSMainEntity='SA_Invoice' and r2.cDMainEntity='SA_Dispatch'
	           			union all
	           			select * from BILL_GEN_RELATION_MAIN r2 where r2.cSMainID=r1.cDMainID and r2.cSMainEntity='SA_Invoice' and r2.cDMainEntity='ST_StkRecord' and r2.cCreateRuleID!='6666'
	           			)
               		)
               	)
               )
               @where[and]
            order by o.dDate DESC, o.cBillCode DESC,l.iNumber
            @order[o.dDate DESC, o.cBillCode DESC,l.iNumber]
            @filter[SA_Order.cCustGUID=CM_Customer.cguid,
            		SA_Order.cCustGUID=CM_Customer.cguid and CM_Customer.cClassGUID=CM_CustomerClass.cGUID,
            		SA_Order.cDeptGUID=cm_department.cguid,
            		SA_Order.cEmpGUID=CM_Employee.cguid,
            		SA_Order.cCreatorGUID=AOS_RMS_USER.cguid,
            		SA_OrderLine.cMatGUID=CM_Material.cGUID,
            		SA_OrderLine.cMatGUID=CM_Material.cGUID and CM_Material.cMatCGUID=CM_MatClass.cGUID,
            		SA_OrderLine.cStoreGUID=CM_Storehouse.cguid,
            		SA_OrderLine.cItemGUID=CM_Project.cguid]
             ]]>
        </i>
        <i id="from_PuOrder" desp="采购订单生成蓝字采购入库单">
        <![CDATA[
        	select top 100 percent x.*
			from(
					SELECT  
						   top 100 percent 
						   0 as iRedFlag,
                    	   b.cguid,
					       a.cCode,
					       b.cHeadGUID,
					       convert(char(10),a.dPODate,120) dDate,
					       sup.cCode AS cSupCode,
					       sup.cName AS cSupName,
					       a.cPurTypeGUID,
					       m.cMatCode,
					       m.cMatName,
					       m.cSpec,
					       m.iBatchFlag,
					       m.iGuaranteeFlag,
					       b.cUnitGUID,
					       CM_Unit_1.cName AS cMUnitName,
					       case when isnull(b.iStkInQTY, 0)=0 then b.iQTY 
					            else case when ((b.iUnitQTY - isnull(b.iStkInQTY, 0))/b.iChangeRate)<0 then 0
					            	 else ((b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))/b.iChangeRate)
					       			 end
					       end as iQTY,
					       case when isnull(b.iStkInQTY, 0)=0 then b.iQTY 
					  			else case when ((b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))/b.iChangeRate)<0 then 0
					            	else ((b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))/b.iChangeRate)
					       			end
					       end as iSInQTY,
					       b.iUnitQTY iOrderUnitQTY,
					       b.iStkInQTY AS iStkInQTY,
					       
					       case when (b.iUnitQTY - isnull(b.iStkInQTY, 0))<0 then 0
					       		else (b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))
					       end as iUnitQTY,
					       
					       b.iTotal AS iOrderiAMT,
					       b.iStkInAMT AS iStkIniAMT,
					       CM_Unit_1.cClassGUID cClassGUID,
					       class.iRateFlag iRateFlag,
					       m.iSubUnit iSubUnit,
					       case when isnull(b.iStkInQTY,0)=0 then b.iUnitPrice_F
					            else b.iTaxPrice_F/(b.iPerTaxRate/100 + 1)
					       end as iUnitPrice_F,	
	                       case when isnull(b.iStkInQTY, 0)=0 then b.iAMT_F
					            when b.cPURefePriceMethod='f' then case when b.iUnitQTY - isnull(b.iStkInQTY, 0)<0 then 0
					            	else ((b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))/b.iChangeRate)*b.iTaxPrice_F/(1+b.iPerTaxRate/100) end
					            else case when b.iUnitQTY - isnull(b.iStkInQTY, 0)<0 then 0
					            	else (b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))*b.iTaxPrice_F/(1+b.iPerTaxRate/100) end
					       end as  iAMT_F, 
			               case when isnull(b.iStkInQTY, 0)=0 then b.iTax_F
					            when b.cPURefePriceMethod='f' then case when b.iUnitQTY - isnull(b.iStkInQTY, 0)<0 then 0 
					            	else ((b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))/b.iChangeRate)*b.iTaxPrice_F*(1-1/(b.iPerTaxRate/100 + 1)) end 
					            else case when b.iUnitQTY - isnull(b.iStkInQTY, 0)<0 then 0
					            	else (b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))*b.iTaxPrice_F*(1-1/(b.iPerTaxRate/100 + 1)) end
					       end as  iTax_F,
			               case when isnull(b.iStkInQTY, 0)=0 then b.iTotal_F
					            when b.cPURefePriceMethod='f' then case when b.iUnitQTY - isnull(b.iStkInQTY, 0)<0 then 0
					            	else ((b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))/b.iChangeRate)*b.iTaxPrice_F end
					            else case when b.iUnitQTY - isnull(b.iStkInQTY, 0)<0 then 0
					            	else (b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))*b.iTaxPrice_F end
					       end as  iTotal_F,
			               b.iTaxPrice_F,
			               case when isnull(b.iStkInQTY,0)=0 then b.iUnitPrice
					            else b.iTaxPrice/(b.iPerTaxRate/100 + 1)
					       end as iUnitPrice,	
	                       case when isnull(b.iStkInQTY, 0)=0 then b.iAMT
					            when b.cPURefePriceMethod='f' then case when b.iUnitQTY - isnull(b.iStkInQTY, 0)<0 then 0 
					            	else ((b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))/b.iChangeRate)*b.iTaxPrice/(1+b.iPerTaxRate/100) end 
					            else case when b.iUnitQTY - isnull(b.iStkInQTY, 0)<0 then 0 
					           		else (b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))*b.iTaxPrice/(1+b.iPerTaxRate/100) end 
					       end as  iAMT, 
			               case when isnull(b.iStkInQTY, 0)=0 then b.iTax
					            when b.cPURefePriceMethod='f' then case when b.iUnitQTY - isnull(b.iStkInQTY, 0)<0 then 0  
					            	else ((b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))/b.iChangeRate)*b.iTaxPrice*(1-1/(b.iPerTaxRate/100 + 1)) end  
					            else case when b.iUnitQTY - isnull(b.iStkInQTY, 0)<0 then 0 
					            	else (b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))*b.iTaxPrice*(1-1/(b.iPerTaxRate/100 + 1)) end 
					       end as  iTax,
			               case when isnull(b.iStkInQTY, 0)=0 then b.iTotal
					            when b.cPURefePriceMethod='f' then case when b.iUnitQTY - isnull(b.iStkInQTY, 0)<0 then 0 
					            	else ((b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))/b.iChangeRate)*b.iTaxPrice end 
					            else case when b.iUnitQTY - isnull(b.iStkInQTY, 0)<0 then 0 
					            	else (b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))*b.iTaxPrice end 
					       end as  iTotal,
			               b.iTaxPrice,
					       b.iPerTaxRate as iTaxRate,
					       b.iChangeRate,
					       b.cBatchGUID as cBatchGUID,
					       a.cTimeStamp,
					       sup.cguid as cSupGUID,
					       m.cguid as cMatGUID,
					       a.cDeptGUID,
					       cd.cName as cDeptName,
					       a.cEmpGUID,
					       ce.cname as cEmpName,
					       b.cMUnitGUID,
					       m.iMaxPrice,
					       m.iPrecision,
					       a.cCode as cOrdCode,
					       a.cCode as cPUOrderCode,
					       b.cFree1,b.cFree2,b.cFree3,b.cFree4,b.cFree5,b.cFree6,b.cFree7,b.cFree8,b.cFree9,b.cFree10,
					       case when ch.iNoCalCost = 0 then b.cStoreGUID 
					       		 else null end as cStoreGUID,
					       case when b.cStoreGUID is null and chm.iNoCalCost = 0 then m.cPositionGUID
					            when m.cStoreGUID = b.cStoreGUID and ch.iNoCalCost = 0 then m.cPositionGUID
					       		else null end as cPositionGUID,
					       case when b.cStoreGUID is null and chm.iNoCalCost = 0 then p.cName
					            when m.cStoreGUID = b.cStoreGUID and ch.iNoCalCost = 0 then p.cName
					       		else null end as crowposinfo,
						   pro.cCode as cItemCode,
						   pro.cName as cItemName,
						   pro.cGUID as cItemGUID,
						   b.iQuotedPrice,
						   case when ISNULL(batchson.dProduction ,0)!=0 then batchson.iGuaranteeDays
						   		else m.iGuaranteePeriod
						   end as iGuaranteeDays,
						   case when ISNULL(batchson.dProduction ,0)!=0 then convert(char(10), batchson.dProduction, 120)
						   		else ''
						   end as dProduction,
						   case when ISNULL(batchson.dProduction ,0)!=0 then convert(char(10), batchson.dOverDate, 120)
						   		else ''
						   end as dOverDate,
						   b.iNumber,
						   b.cPURefePriceMethod,
						   b.cPURefePriceUnitGUID,
						   case when b.cPURefePriceMethod='f' then case when isnull(b.iStkInQTY, 0)=0 then b.iRefePriceQTY 
					            										else case when b.iUnitQTY - isnull(b.iStkInQTY, 0)<0 then 0
					            											else ((b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))/b.iChangeRate) end end
					       		else case when isnull(b.iStkInQTY, 0)=0 then b.iRefePriceQTY 
					            										else case when b.iUnitQTY - isnull(b.iStkInQTY, 0)<0 then 0
					            											else ((b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))) end end
					       end	as iRefePriceQTY,
					       a.cRemark cRemark1,
					       b.cRemark,
					       stkInInfo.stkInReturnQTY,
					       /*2019-5-16 重算折扣额  tss*/
					       case when isnull(b.iStkInQTY, 0)=0 then b.idisamt_f
					            else (b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))/(b.iUnitQTY -isnull(stkInInfo.stkInReturnQTY,0))*b.idisamt_f
					       end as  idisamt_f,
					       b.czcdetailid
					       @field[a.*,b.*]	
					       @flex[a] 
		                   @flex[b]  
					  FROM 
					  		PU_OrderLine b
					  		left join CM_Material m on m.cGUID = b.cMatGUID
					  		left join CM_Position p on m.cPositionGUID = p.cGUID
					  		left join CM_Unit CM_Unit_1 on b.cMUnitGUID = CM_Unit_1.cGUID
					  		left JOIN CM_UnitClass class on CM_Unit_1.cClassGUID = class.cGUID 
					 		left join CM_Unit u2 on b.cUnitGUID = u2.cGUID
					 		left join PU_Order a on b.cHeadGUID = a.cGUID
					 		left join CM_Project pro on b.cItemGUID = pro.cGUID
							left join cm_department as cd on cd.cguid = a.cDeptGUID
							left join cm_employee as ce on ce.cguid = a.cEmpGUID
							left join CM_Supplier sup on a.cSupGUID = sup.cGUID
					 		left join CM_MatClass mc on m.cMatCGUID = mc.cguid
					 		left join CM_SupplierClass sc on sup.cClassGUID = sc.cguid
					 		left join (select relation.csLineID,
											  sum(isnull(stl.iUnitQTY,0)) stkInReturnQTY
										from BILL_GEN_RELATION_DETAIL relation
										     left join ST_StkRecordLine stl on stl.cGUID=relation.cDLineID
										     left join ST_StkRecord st on stl.cHeadGUID=st.cGUID
										where relation.cSMainEntity='PU_Order'
											and st.iReturnFlag=1
											and st.iRedFlag=1 
										GROUP BY relation.csLineID)stkInInfo on b.cGUID=stkInInfo.csLineID
							left join CO_BusinessProcess cbp on cbp.cBusType = a.cBusType and cbp.cGUID = a.cBusProcess
							left join CO_Process cp on cp.cProcessID = cbp.cSystemFlowGUID and cp.cSBillType='076' and cp.cDBillType='010'
							left join CM_Storehouse ch on b.cStoreGUID=ch.cGUID	
							left join CM_Storehouse chm on m.cStoreGUID=chm.cGUID	
							LEFT JOIN (SELECT DISTINCT
										matbatch.dProduction,
										matbatch.iGuaranteeDays,
										matbatch.dOverDate,
										matbatch.cMatGUID,
										matbatch.cBatchGUID 
									FROM
										ST_MaBatchAmount matbatch
										INNER JOIN PU_OrderLine re ON re.cMatGUID = matbatch.cMatGUID 
										AND re.cBatchGUID= matbatch.cBatchGUID 
									WHERE
										matbatch.dProduction != '' 
										OR matbatch.dProduction IS NOT NULL) batchson on batchson.cBatchGUID=b.cBatchGUID and batchson.cMatGUID=b.cMatGUID
					 		@table[PU_Order a,PU_OrderLine b,CM_Material m]
					 WHERE a.cTaxType='plus'
						   and a.iAuditStatus = 'checked'
						   and (abs(b.iUnitQTY) - abs(isnull(b.iStkInQTY,0)+isnull(stkInInfo.stkInReturnQTY,0))>0 or ({iAllOrder:0}=1 and isnull(b.iStkInQTY,0)>0 and b.iUnitQTY <=isnull(b.iStkInQTY,0))) 
						   and $equal(a.cSupGUID,cSupGUID)
						   and $between(a.dPODate,dDateLower,dDateUpper)
						   and $between(a.cCode,cCodeLower,cCodeUpper)
						   and $equal(b.cguid,cGUID)
						   and (a.iCloseFlag != 1 or a.iCloseFlag is null)
					   	   and (b.iCloseFlag != 1 or b.iCloseFlag is null)
						   and $between(m.cMatCode,cMatCodeLower,cMatCodeUpper)	 
						   and $equal(b.cItemGUID,cItemGUID)
						   and (m.iServisFlag is null or m.iServisFlag != 1)
						   and $like(b.cBatchGUID,cBatchGUID)
						   and $equal(a.cguid,cPushGUID)
						   and $in(b.czcdetailid,czcsadetailids)
						   and ( '*' = {matClass:'*'} OR m.cFullCode LIKE (SELECT cFullCode + '!%' FROM CM_MatClass mc WHERE  $equal(cCode,matClass)) )
						   and $like(b.cRemark,cRemark)	
						   and not exists(select 1 from BILL_GEN_RELATION_DETAIL bg left join ST_StkRecord st on bg.cDMainID=st.cGUID 
					    						where bg.cSLineID=b.cGUID and bg.cDMainEntity='st_stkrecord' and st.cbillType ='010' and st.iRedFlag<>'1')
					       and $equal(a.cBusProcess,cBusProcess) 
					       /*订单没有直接或间接生成过到货单*/
					       and not exists(select 1 from BILL_GEN_RELATION_MAIN bg where bg.cSLineID=b.cGUID and bg.cDMainEntity='PU_Receive')
					       and not exists(select 1 from BILL_GEN_RELATION_DETAIL bg where bg.cSLineID=b.cGUID and bg.cDMainEntity='PU_Receive')
						   /*是否只查质检物品*/
						   and ({iAllmat:1}=1 or ({iAllmat:1}=0 and m.iPUCheck!=1))
						   @where[and]
						union all
						SELECT  
						   top 100 percent
						   0 as iRedFlag, 
                    	   b.cguid,
					       a.cCode,
					       b.cHeadGUID,
					       convert(char(10),a.dPODate,120) dDate,
					       sup.cCode AS cSupCode,
					       sup.cName AS cSupName,
					       a.cPurTypeGUID,
					       m.cMatCode,
					       m.cMatName,
					       m.cSpec,
					       m.iBatchFlag,
					       m.iGuaranteeFlag,
					       b.cUnitGUID,
					       CM_Unit_1.cName AS cMUnitName,
					       case when isnull(b.iStkInQTY, 0)=0 then b.iQTY 
					            else case when ((b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))/b.iChangeRate)<0 then 0
					            	 else ((b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))/b.iChangeRate)
					       			 end
					       end as iQTY,
					       case when isnull(b.iStkInQTY, 0)=0 then b.iQTY 
					       		else case when ((b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))/b.iChangeRate)<0 then 0
					            	else ((b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))/b.iChangeRate)
					       			end
					       end as iSInQTY,
					       b.iUnitQTY iOrderUnitQTY,
					       b.iStkInQTY AS iStkInQTY,
					       
					       case when (b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))<0 then 0
					       		else (b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))
					       end as iUnitQTY,
					       
					       b.iTotal AS iOrderiAMT,
					       b.iStkInAMT AS iStkIniAMT,
					       CM_Unit_1.cClassGUID cClassGUID,
					       class.iRateFlag iRateFlag,
					       m.iSubUnit iSubUnit,
					       case when isnull(b.iStkInQTY,0)=0 then b.iUnitPrice_F
					            else b.iTaxPrice_F*(1-b.iPerTaxRate/100)
					       end as iUnitPrice_F,	
	                       case when isnull(b.iStkInQTY, 0)=0 then b.iAMT_F
					            when b.cPURefePriceMethod='f' then ((b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))/b.iChangeRate)*b.iTaxPrice_F*(1-b.iPerTaxRate/100) 
					            else (b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))*b.iTaxPrice_F*(1-b.iPerTaxRate/100)
					       end as  iAMT_F, 
			               case when isnull(b.iStkInQTY, 0)=0 then b.iTax_F
					            when b.cPURefePriceMethod='f' then ((b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))/b.iChangeRate)*b.iTaxPrice_F*b.iPerTaxRate/100
					            else (b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))*b.iTaxPrice_F*b.iPerTaxRate/100
					       end as  iTax_F,
			               case when isnull(b.iStkInQTY, 0)=0 then b.iTotal_F
					            when b.cPURefePriceMethod='f' then ((b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))/b.iChangeRate)*b.iTaxPrice_F 
					            else (b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))*b.iTaxPrice_F
					       end as  iTotal_F,
			               b.iTaxPrice_F,
			               case when isnull(b.iStkInQTY,0)=0 then b.iUnitPrice
					            else b.iTaxPrice*(1-b.iPerTaxRate/100)
					       end as iUnitPrice,	
	                       case when isnull(b.iStkInQTY, 0)=0 then b.iAMT
					            when b.cPURefePriceMethod='f' then ((b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))/b.iChangeRate)*b.iTaxPrice*(1-b.iPerTaxRate/100)
					            else (b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))*b.iTaxPrice*(1-b.iPerTaxRate/100) 
					       end as  iAMT, 
			               case when isnull(b.iStkInQTY, 0)=0 then b.iTax
					            when b.cPURefePriceMethod='f' then ((b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))/b.iChangeRate)*b.iTaxPrice*b.iPerTaxRate/100
					            else (b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))*b.iTaxPrice*b.iPerTaxRate/100
					       end as  iTax,
			               case when isnull(b.iStkInQTY, 0)=0 then b.iTotal
					            when b.cPURefePriceMethod='f' then ((b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))/b.iChangeRate)*b.iTaxPrice 
					            else (b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))*b.iTaxPrice
					       end as  iTotal,
			               b.iTaxPrice,
					       b.iPerTaxRate as iTaxRate,
					       b.iChangeRate,
					       b.cBatchGUID as cBatchGUID,
					       a.cTimeStamp,
					       sup.cguid as cSupGUID,
					       m.cguid as cMatGUID,
					       a.cDeptGUID,
					       cd.cName as cDeptName,
					       a.cEmpGUID,
					       ce.cname as cEmpName,
					       b.cMUnitGUID,
					       m.iMaxPrice,
					       m.iPrecision,
					       a.cCode as cOrdCode,
					       a.cCode as cPUOrderCode,
					       b.cFree1,b.cFree2,b.cFree3,b.cFree4,b.cFree5,b.cFree6,b.cFree7,b.cFree8,b.cFree9,b.cFree10,
					       case when b.cStoreGUID is null and chm.iNoCalCost = 0 then m.cStoreGUID
					       		 when ch.iNoCalCost = 0 then b.cStoreGUID 
					       		 else null end as cStoreGUID,
					       case when b.cStoreGUID is null and chm.iNoCalCost = 0 then m.cPositionGUID
					            when m.cStoreGUID = b.cStoreGUID and ch.iNoCalCost = 0 then m.cPositionGUID
					       		else null end as cPositionGUID,	
					       case when b.cStoreGUID is null and chm.iNoCalCost = 0 then p.cName
					            when m.cStoreGUID = b.cStoreGUID and ch.iNoCalCost = 0 then p.cName
					       		else null end as crowposinfo, 
						   pro.cCode as cItemCode,
						   pro.cName as cItemName,
						   pro.cGUID as cItemGUID,
						   b.iQuotedPrice,
						   case when ISNULL(batchson.dProduction ,0)!=0 then batchson.iGuaranteeDays
						   		else m.iGuaranteePeriod
						   end as iGuaranteeDays,
						   case when ISNULL(batchson.dProduction ,0)!=0 then convert(char(10), batchson.dProduction, 120)
						   		else ''
						   end as dProduction,
						   case when ISNULL(batchson.dProduction ,0)!=0 then convert(char(10), batchson.dOverDate, 120)
						   		else ''
						   end as dOverDate,
						   b.iNumber,
						   b.cPURefePriceMethod,
						   b.cPURefePriceUnitGUID,
						   case when b.cPURefePriceMethod='f' then case when isnull(b.iStkInQTY, 0)=0 then b.iRefePriceQTY 
					            										else ((b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))/b.iChangeRate) end 
					       		else case when isnull(b.iStkInQTY, 0)=0 then b.iRefePriceQTY 
					            										else ((b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))) end
					       end	as iRefePriceQTY,
					       a.cRemark cRemark1,
					       b.cRemark,
					       stkInInfo.stkInReturnQTY,
					       /*2019-5-16 重算折扣额  tss*/
					       case when isnull(b.iStkInQTY, 0)=0 then b.idisamt_f
					            else (b.iUnitQTY - isnull(b.iStkInQTY, 0)-isnull(stkInInfo.stkInReturnQTY,0))/(b.iUnitQTY -isnull(stkInInfo.stkInReturnQTY,0))*b.idisamt_f
					       end as  idisamt_f,
					       b.czcdetailid
					       @field[a.*,b.*]	
					       @flex[a] 
		                   @flex[b]  
					  FROM 
					  		PU_OrderLine b
					  		left join CM_Material m on m.cGUID = b.cMatGUID
					  		left join CM_Position p on m.cPositionGUID = p.cGUID
					  		left join CM_Unit CM_Unit_1 on b.cMUnitGUID = CM_Unit_1.cGUID
					  		left JOIN CM_UnitClass class on CM_Unit_1.cClassGUID = class.cGUID 
					 		left join CM_Unit u2 on b.cUnitGUID = u2.cGUID
					 		left join PU_Order a on b.cHeadGUID = a.cGUID
					 		left join CM_Project pro on b.cItemGUID = pro.cGUID
							left join cm_department as cd on cd.cguid = a.cDeptGUID
							left join cm_employee as ce on ce.cguid = a.cEmpGUID
							left join CM_Supplier sup on a.cSupGUID = sup.cGUID
					 		left join CM_MatClass mc on m.cMatCGUID = mc.cguid
					 		left join CM_SupplierClass sc on sup.cClassGUID = sc.cguid
					 		left join (select relation.csLineID,
											  sum(isnull(stl.iUnitQTY,0)) stkInReturnQTY
										from BILL_GEN_RELATION_DETAIL relation
										     left join ST_StkRecordLine stl on stl.cGUID=relation.cDLineID
										     left join ST_StkRecord st on stl.cHeadGUID=st.cGUID
										where relation.cSMainEntity='PU_Order'
											and st.iReturnFlag=1
											and st.iRedFlag=1 
										GROUP BY relation.csLineID)stkInInfo on b.cGUID=stkInInfo.csLineID
							left join CO_BusinessProcess cbp on cbp.cBusType = a.cBusType and cbp.cGUID = a.cBusProcess
							left join CO_Process cp on cp.cProcessID = cbp.cSystemFlowGUID and cp.cSBillType='076' and cp.cDBillType='010'
							left join CM_Storehouse ch on b.cStoreGUID=ch.cGUID	
							left join CM_Storehouse chm on m.cStoreGUID=chm.cGUID
							LEFT JOIN (SELECT DISTINCT
										matbatch.dProduction,
										matbatch.iGuaranteeDays,
										matbatch.dOverDate,
										matbatch.cMatGUID,
										matbatch.cBatchGUID 
									FROM
										ST_MaBatchAmount matbatch
										INNER JOIN PU_OrderLine re ON re.cMatGUID = matbatch.cMatGUID 
										AND re.cBatchGUID= matbatch.cBatchGUID 
									WHERE
										matbatch.dProduction != '' 
										OR matbatch.dProduction IS NOT NULL) batchson on batchson.cBatchGUID=b.cBatchGUID and batchson.cMatGUID=b.cMatGUID
					 		@table[PU_Order a,PU_OrderLine b,CM_Material m]
					 WHERE a.cTaxType='contain'
						   and a.iAuditStatus = 'checked'
						   and (abs(b.iUnitQTY) - abs(isnull(b.iStkInQTY,0)+isnull(stkInInfo.stkInReturnQTY,0))>0 or ({iAllOrder:0}=1 and isnull(b.iStkInQTY,0)>0 and b.iUnitQTY <=isnull(b.iStkInQTY,0))) 
						   and $equal(a.cSupGUID,cSupGUID)
						   and $between(a.dPODate,dDateLower,dDateUpper)
						   and $between(a.cCode,cCodeLower,cCodeUpper)
						   and $equal(b.cguid,cGUID)
						   and (a.iCloseFlag != 1 or a.iCloseFlag is null)
					   	   and (b.iCloseFlag != 1 or b.iCloseFlag is null)
						   and $between(m.cMatCode,cMatCodeLower,cMatCodeUpper)	 
						   and $equal(b.cItemGUID,cItemGUID)
						   and (m.iServisFlag is null or m.iServisFlag != 1)
						   and $like(b.cBatchGUID,cBatchGUID)	
						   and $like(b.cRemark,cRemark)
						   and $equal(a.cguid,cPushGUID)
						   and $in(b.czcdetailid,czcsadetailids)
						   and ( '*' = {matClass:'*'} OR m.cFullCode LIKE (SELECT cFullCode + '!%' FROM CM_MatClass mc WHERE  $equal(cCode,matClass)) )
						   and not exists(select 1 from BILL_GEN_RELATION_DETAIL bg left join ST_StkRecord st on bg.cDMainID=st.cGUID 
					    						where bg.cSLineID=b.cGUID and bg.cDMainEntity='st_stkrecord' and st.cbillType ='010' and st.iRedFlag<>'1')
					       and $equal(a.cBusProcess,cBusProcess) 
						   /*订单没有直接或间接生成过到货单*/
					       and not exists(select 1 from BILL_GEN_RELATION_MAIN bg where bg.cSLineID=b.cGUID and bg.cDMainEntity='PU_Receive')
					       and not exists(select 1 from BILL_GEN_RELATION_DETAIL bg where bg.cSLineID=b.cGUID and bg.cDMainEntity='PU_Receive')
						   /*是否只查质检物品*/
						   and ({iAllmat:1}=1 or ({iAllmat:1}=0 and m.iPUCheck!=1))
						   @where[and]
					)x
					 order by 
					 		dDate desc, cCode desc,iNumber
					 		@order[dDate desc, cCode desc,iNumber]
					 	@filter[PU_Order.cSupGUID=cm_supplier.cguid,PU_Order.cSupGUID=cm_supplier.cguid and CM_Supplier.cClassGUID=CM_SupplierClass.cGUID,PU_Order.cDeptGUID=cm_department.cguid,PU_Order.cCreatorGUID=AOS_RMS_USER.cguid,
					 			PU_Order.cEmpGUID=CM_Employee.cGUID,PU_OrderLine.cMatGUID=CM_Material.cGUID and CM_Material.cMatCGUID = CM_MatClass.cGUID,PU_OrderLine.cItemGUID=CM_Project.cGUID]
        ]]>
        </i>
    </sql>
</sqls>